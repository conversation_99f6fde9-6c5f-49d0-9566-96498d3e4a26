{"timestamp": "2025-08-05T15:44:22.279031", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-05T15:44:33.382084", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-05T15:44:36.534222", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-05T15:44:36.552227", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-05T15:44:36.554279", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:8000", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-05T15:44:36.555230", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-05T15:44:36.555908", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-05T15:44:36.556600", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-05T15:45:21.276755", "level": "INFO", "logger": "main", "message": "Processing document: data2.pdf (541643 bytes)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-05T15:45:21.277831", "level": "INFO", "logger": "main", "message": "Starting intelligent document analysis for: data2.pdf", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-05T15:45:21.278629", "level": "INFO", "logger": "agents", "message": "Starting document analysis for: data2.pdf", "module": "agents", "function": "process_document", "line": 378}
{"timestamp": "2025-08-05T15:45:24.781417", "level": "INFO", "logger": "agents", "message": "Document analysis for data2.pdf: STANDARD_MODE (confidence: 0.95)", "module": "agents", "function": "analyze_document", "line": 186}
{"timestamp": "2025-08-05T15:45:24.782509", "level": "INFO", "logger": "agents", "message": "Routing data2.pdf to STANDARD_MODE", "module": "agents", "function": "process_document", "line": 383}
{"timestamp": "2025-08-05T15:45:24.783364", "level": "INFO", "logger": "agents", "message": "Processing data2.pdf in STANDARD_MODE", "module": "agents", "function": "_process_standard_mode", "line": 415}
{"timestamp": "2025-08-05T15:45:31.050005", "level": "INFO", "logger": "main", "message": "Document routing decision for data2.pdf:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-05T15:45:31.051312", "level": "INFO", "logger": "main", "message": "  - Processing Mode: STANDARD_MODE", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-05T15:45:31.052250", "level": "INFO", "logger": "main", "message": "  - Confidence: 0.95", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-05T15:45:31.053004", "level": "INFO", "logger": "main", "message": "  - Document Category: other", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-05T15:45:31.055453", "level": "INFO", "logger": "main", "message": "  - Predicted Content: printed", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-05T15:45:31.056181", "level": "INFO", "logger": "main", "message": "  - Reasoning: The document is a PDF file, which is the primary indicator for STANDARD_MODE. The filename does not contain any handwriting or invoice/receipt keywords, suggesting it is a standard digital document. Given the file type and lack of indicators for handwritten content, it is highly likely that this document should be processed in STANDARD_MODE. (PDF - standard processing)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-05T15:45:36.735112", "level": "INFO", "logger": "main", "message": "CSV result saved: results\\20250805_154536_c4fc46ac_data2.csv", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-05T15:45:36.736633", "level": "INFO", "logger": "main", "message": "Document processed successfully: data2.pdf -> STANDARD_MODE (15.46s)", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-05T15:46:45.369569", "level": "INFO", "logger": "main", "message": "Starting complete database refresh - deleting all CSV files and clearing vector DB", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-05T15:46:45.703048", "level": "INFO", "logger": "main", "message": "Database refresh completed successfully:", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-05T15:46:45.703985", "level": "INFO", "logger": "main", "message": "  - CSV files deleted: 2", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-05T15:46:45.704816", "level": "INFO", "logger": "main", "message": "  - Vector DB cleared: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-05T15:46:45.705632", "level": "INFO", "logger": "main", "message": "  - Documents loaded: 14", "module": "unified_logging", "function": "info", "line": 244}
