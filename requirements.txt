# Automated Invoice Extractor
# Consolidated requirements for all components

# ================================
# CORE FRAMEWORK
# ================================
fastapi
uvicorn[standard]
pydantic
pydantic-settings
python-dotenv
python-multipart
aiofiles

# ================================
# AUTOGEN FRAMEWORK
# ================================
pyautogen

# ================================
# LLM AND AI SERVICES
# ================================
# Groq Integration
groq
langchain-groq

# LangChain Framework
langchain
langchain-core
langchain-community
instructor
pymupdf
pypdf2
pillow
opencv-python
numpy
matplotlib
pytesseract
docling
chromadb
langchain-chroma
sentence-transformers
langchain-huggingface
pandas
numpy
langdetect
requests
httpx
pytest
pytest-asyncio
black
flake8
structlog
rich
pathlib2
uuid
typing-extensions
