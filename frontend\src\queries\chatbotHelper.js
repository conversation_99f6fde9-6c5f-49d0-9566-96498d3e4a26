import axios from "axios";


const chatBotUrl = 'http://localhost:6969'

export const handleSend = async (textInput, setTextInput, setIsLoading, setMessages) => {
  if (!textInput.trim()) return;

  const userMessage = { text: textInput.trim(), isUser: true };
  setMessages((prev) => [...prev, userMessage]);
  setIsLoading(true);
  setTextInput("");

  console.log('User message:', textInput);

  // Simulate typing delay
  await new Promise(resolve => setTimeout(resolve, 1000));

  try {
    // Try to connect to backend first
    const botMessageResponse = await axios.post(`${chatBotUrl}/chatbot`,
      { message: textInput },
      { timeout: 5000 }
    );
    const botMessage = botMessageResponse.data.response;
    console.log('Bot message from server:', botMessage);

    setMessages((prev) => [...prev, { text: botMessage, isUser: false }]);
  } catch (error) {
    console.log("Backend not available, using offline responses");

    // Offline chatbot responses
    const responses = {
      'hello': 'Hello! I\'m your file processing assistant. How can I help you today?',
      'hi': 'Hi there! I can help you with file processing questions.',
      'help': 'I can assist you with:\n• File upload and processing\n• Excel/CSV data extraction\n• Download processed files\n• General questions about the application',
      'how to upload': 'To upload files:\n1. Choose "Upload Folder" or "Upload Files"\n2. Select your files (Excel, CSV, images, PDFs)\n3. Wait for processing to complete\n4. View and download results',
      'download': 'To download processed files:\n1. Click "Show Data" to preview\n2. Click "Download CSV" to save the processed file\n3. The file will be saved to your Downloads folder',
      'preview': 'You can preview files by:\n• Click "Input Preview" to see original file\n• Click "Show Data" to see processed data\n• Both previews help you verify the results',
      'default': 'I\'m here to help with file processing! You can ask me about uploading files, viewing previews, downloading results, or any other questions about this application.'
    };

    const lowerInput = textInput.toLowerCase();
    let botResponse = responses.default;

    // Find matching response
    for (const [key, response] of Object.entries(responses)) {
      if (lowerInput.includes(key)) {
        botResponse = response;
        break;
      }
    }

    setMessages((prev) => [...prev, { text: botResponse, isUser: false }]);
  } finally {
    setIsLoading(false);
  }
};
  
 

export const refreshDb = async () => {
    console.log('delete');
    
  try {
    const response = await axios.delete(`${chatBotUrl}/refresh-db`);
    console.log('Database refreshed successfully:', response.data);
    return response.data;  // You can return the response if needed
  } catch (error) {
    console.error('Error refreshing the database:', error);
    throw error;  // Re-throw the error if you want to handle it further up the chain
  }
};
