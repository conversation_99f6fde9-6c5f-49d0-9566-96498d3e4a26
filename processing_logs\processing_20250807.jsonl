{"timestamp": "2025-08-07T11:41:35.176483", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-07T11:41:42.637541", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-07T11:41:45.358356", "level": "INFO", "logger": "main", "message": "All processors initialized successfully", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-07T11:41:45.374946", "level": "INFO", "logger": "main", "message": "Starting Agentic Document Processing System v2.0.0", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-07T11:41:45.375816", "level": "INFO", "logger": "main", "message": "Server: 0.0.0.0:6969", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-07T11:41:45.376408", "level": "INFO", "logger": "main", "message": "Debug mode: False", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-07T11:41:45.376994", "level": "INFO", "logger": "main", "message": "Groq API configured: True", "module": "unified_logging", "function": "info", "line": 244}
{"timestamp": "2025-08-07T11:41:45.377664", "level": "INFO", "logger": "main", "message": "Application startup complete", "module": "unified_logging", "function": "info", "line": 244}
